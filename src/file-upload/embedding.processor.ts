import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Embedding } from './embedding.entity';
import { LLMService } from './openai.service';
import { FileProcessorService } from './file-processor.service';
import * as cheerio from 'cheerio';
import * as yaml from 'js-yaml';
import * as jwt from 'atlassian-jwt';

interface EmbeddingJob {
  fileId: string;
  fileBuffer: Buffer;
  mimetype: string;
  url?: string;
  isPublishUrl?: boolean;
  integration?: string;
  integration_email?: string;
  integration_api_token?: string;
}

@Processor('embeddings')
export class EmbeddingProcessor extends WorkerHost {
  private readonly logger = new Logger(EmbeddingProcessor.name);

  constructor(
    @InjectRepository(Embedding)
    private embeddingRepository: Repository<Embedding>,
    private openAIService: LLMService,
    private fileProcessorService: FileProcessorService,
  ) {
    super();
  }

  private async processUrlContent(url: string, integration?: string, credentials?: { email: string; token: string }): Promise<string> {
    try {
      if (integration) {
        // Handle private URL (e.g., Confluence)
        return await this.fetchPrivateUrlContent(url, integration, credentials);
      }

      // Check if this is a Swagger UI URL and handle it specially
      if (this.isSwaggerUrl(url)) {
        return await this.processSwaggerUrl(url);
      }

      // Determine content type before fetching full content
      // Use GET instead of HEAD as some servers don't support HEAD properly
      let headResponse: Response;
      try {
        headResponse = await fetch(url, { method: 'HEAD' });
      } catch (headError) {
        // If HEAD fails, try GET with a small range to check content type
        this.logger.debug(`HEAD request failed for ${url}, trying GET`);
        headResponse = await fetch(url);
      }

      const contentType = headResponse.headers.get('content-type') || '';

      // Skip non-text content types
      if (!contentType.includes('text/html') &&
          !contentType.includes('text/plain') &&
          !contentType.includes('application/json')) {
        this.logger.warn(`Skipping non-text URL: ${url} (${contentType})`);
        return '';
      }

      // Handle public URL
      const response = headResponse.status === 200 ? headResponse : await fetch(url);
      const content = await response.text();

      // If it's JSON, return it directly (formatted)
      if (contentType.includes('application/json')) {
        try {
          const jsonData = JSON.parse(content);
          return JSON.stringify(jsonData, null, 2);
        } catch (jsonError) {
          this.logger.warn(`Failed to parse JSON from ${url}:`, jsonError);
          return content;
        }
      }

      return this.cleanHtmlContent(content);
    } catch (error) {
      this.logger.error(`Error processing URL ${url}:`, error);
      throw new Error(`Failed to process URL: ${error.message}`);
    }
  }

  private async cleanHtmlContent(html: string): Promise<string> {
    const cheerio = await import('cheerio');
    const $ = cheerio.load(html);
    
    // Remove common non-relevant elements
    $('script, style, noscript, iframe, video, audio, svg, canvas, footer, nav, header, aside').remove();
    $('[class*="advert"], [class*="banner"], [class*="promo"], [id*="ad-"], [class*="social"]').remove();
    $('[class*="cookie"], [class*="popup"], [class*="modal"], [class*="overlay"]').remove();
    $('[class*="animation"], [class*="slider"], [class*="carousel"]').remove();
    
    // Extract the main content
    const contentSelectors = [
      'article', 'main', '.content', '#content', '.post', '.entry', 
      '.documentation', '.docs', '.main-content', '[role="main"]'
    ];
    
    let mainContent = '';
    
    // Try to find main content container
    for (const selector of contentSelectors) {
      if ($(selector).length) {
        mainContent = $(selector).text();
        break;
      }
    }
    
    // If no main content found, extract body text with fallback
    if (!mainContent.trim()) {
      mainContent = $('body').text();
    }
    
    // Clean up the text
    let cleanedText = mainContent
      .replace(/\s+/g, ' ')
      .replace(/\n+/g, '\n')
      .trim();
      
    // Handle empty or very short content
    if (cleanedText.length < 100) {
      this.logger.warn('Content too short after cleaning');
      const title = $('title').text();
      const description = $('meta[name="description"]').attr('content') || '';
      cleanedText = `${title}\n\n${description}`;
    }
    
    return cleanedText;
  }
  
  private async fetchPrivateUrlContent(url: string, integration: string, credentials?: { email: string; token: string }): Promise<string> {
    try {
      let content = '';

      switch (integration.toLowerCase()) {
        case 'atlassian': {
          if (!credentials?.email || !credentials?.token) {
            throw new Error('Atlassian email and API token are required');
          }

          // Determine if it's a Confluence or JIRA URL
          const isConfluence = url.includes('/wiki/') || url.includes('/pages/') || url.includes('/display/');
          const isJira = url.includes('/browse/');

          if (isConfluence) {
            // Extract page ID from Confluence URL
            const pageIdMatch = url.match(/pages\/(\d+)/);
            if (!pageIdMatch) {
              throw new Error('Invalid Confluence URL format');
            }
            const pageId = pageIdMatch[1];

            // Extract base URL (e.g., https://your-domain.atlassian.net/wiki)
            const baseUrlMatch = url.match(/^(https?:\/\/[^\/]+(?:\/wiki|\/display)?)\/?/);
            let baseUrl = baseUrlMatch ? baseUrlMatch[1] : '';
            const normalizedBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

            const apiUrl = `${normalizedBaseUrl}/rest/api/content/${pageId}?expand=body.storage`;
            this.logger.debug(`Fetching Confluence content for page ID: ${pageId} from URL: ${apiUrl}`);

            const response = await fetch(apiUrl, {
              headers: {
                'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.token}`).toString('base64')}`,
                'Accept': 'application/json',
              },
            });

            if (!response.ok) {
              let errorMessage = `Confluence API error: ${response.status} ${response.statusText}`;
              if (response.status === 401) {
                errorMessage = 'Confluence API error: Unauthorized. Check your credentials.';
              } else if (response.status === 404) {
                errorMessage = 'Confluence API error: Page not found.';
              } else if (response.status === 500) {
                errorMessage = 'Confluence API error: Internal server error.';
              }
              this.logger.error(errorMessage);
              throw new Error(errorMessage);
            }

            const data = await response.json();
            
            if (!data.body?.storage?.value) {
              throw new Error('No content found in Confluence response');
            }

            // Convert Confluence storage format (HTML) to plain text
            const $ = cheerio.load(data.body.storage.value);
            content = $.text();

            // Clean up the content
            content = content
              .replace(/\s+/g, ' ')
              .replace(/\n+/g, '\n')
              .trim();

            this.logger.debug(`Successfully fetched Confluence content for page ${pageId}`);
          } else if (isJira) {
            // Extract issue key from JIRA URL
            const issueKeyMatch = url.match(/\/browse\/([A-Z]+-\d+)/);
            if (!issueKeyMatch) {
              throw new Error('Invalid JIRA URL format');
            }
            const issueKey = issueKeyMatch[1];

            // Extract base URL (e.g., https://your-domain.atlassian.net)
            const baseUrlMatch = url.match(/^(https?:\/\/[^\/]+)/);
            if (!baseUrlMatch) {
              throw new Error('Invalid JIRA URL format');
            }
            const baseUrl = baseUrlMatch[1];

            // Fetch issue details using JIRA REST API
            const apiUrl = `${baseUrl}/rest/api/2/issue/${issueKey}?fields=summary,description,attachment,customfield_10000`;
            this.logger.debug(`Fetching JIRA content for issue: ${issueKey} from URL: ${apiUrl}`);

            const response = await fetch(apiUrl, {
              headers: {
                'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.token}`).toString('base64')}`,
                'Accept': 'application/json',
              },
            });

            if (!response.ok) {
              let errorMessage = `JIRA API error: ${response.status} ${response.statusText}`;
              if (response.status === 401) {
                errorMessage = 'JIRA API error: Unauthorized. Check your credentials.';
              } else if (response.status === 404) {
                errorMessage = 'JIRA API error: Issue not found.';
              }
              this.logger.error(errorMessage);
              throw new Error(errorMessage);
            }

            const data = await response.json();
            
            // Extract relevant fields
            const summary = data.fields.summary || '';
            const description = data.fields.description || '';
            
            // Try to get acceptance criteria (often stored in customfield_10000 as "Acceptance Criteria")
            // Note: The actual field ID may vary between JIRA instances
            let acceptanceCriteria = '';
            if (data.fields.customfield_10000) {
              acceptanceCriteria = data.fields.customfield_10000;
            }
            
            // Combine the content
            content = `JIRA Issue: ${issueKey}\n\nSummary: ${summary}\n\nDescription: ${description}\n\nAcceptance Criteria: ${acceptanceCriteria}`;
            
            // Handle attachments if available
            if (data.fields.attachment && data.fields.attachment.length > 0) {
              // Get the first few attachments (limit to avoid overloading)
              const attachmentsToProcess = data.fields.attachment.slice(0, 3);
              
              for (const attachment of attachmentsToProcess) {
                if (this.isTextBasedAttachment(attachment.filename)) {
                  try {
                    // Download attachment content
                    const attachmentResponse = await fetch(attachment.content, {
                      headers: {
                        'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.token}`).toString('base64')}`,
                      },
                    });
                    
                    if (attachmentResponse.ok) {
                      const attachmentContent = await attachmentResponse.text();
                      content += `\n\nAttachment (${attachment.filename}):\n${attachmentContent}`;
                    }
                  } catch (attachmentError) {
                    this.logger.warn(`Failed to fetch attachment ${attachment.filename}: ${attachmentError.message}`);
                  }
                }
              }
            }
            
            this.logger.debug(`Successfully fetched JIRA content for issue ${issueKey}`);
          } else {
            throw new Error('Unsupported Atlassian URL format. Must be a Confluence or JIRA URL.');
          }
          break;
        }

        case 'sharepoint':
          // Implement SharePoint API call here
          this.logger.debug(`Fetching content from SharePoint URL: ${url}`);
          throw new Error('SharePoint integration not implemented yet');

        default:
          throw new Error(`Unsupported integration: ${integration}`);
      }

      if (!content) {
        throw new Error(`Failed to fetch content from private URL: ${url}`);
      }

      return content;
    } catch (error) {
      this.logger.error(`Error fetching private URL content: ${url}`, error);
      throw error;
    }
  }

  // Helper method to determine if an attachment is text-based
  private isTextBasedAttachment(filename: string): boolean {
    const textExtensions = ['.txt', '.md', '.json', '.csv', '.xml', '.html', '.js', '.ts', '.py', '.java', '.c', '.cpp', '.h', '.cs'];
    const extension = filename.substring(filename.lastIndexOf('.')).toLowerCase();
    return textExtensions.includes(extension);
  }

  // Helper method to detect API documentation URLs (Swagger, OpenAPI, and other formats)
  private isSwaggerUrl(url: string): boolean {
    const apiDocPatterns = [
      // Swagger UI patterns
      /\/swagger\//i,
      /\/swagger-ui\//i,
      /\/swaggerui\//i,
      /swagger.*\.html/i,

      // OpenAPI patterns
      /\/openapi\//i,
      /openapi.*\.html/i,
      /openapi.*\.json/i,
      /openapi.*\.yaml/i,
      /openapi.*\.yml/i,

      // API documentation patterns
      /\/api-docs\//i,
      /\/api\/docs\//i,
      /\/docs\/api\//i,
      /\/docs\//i,
      /\/documentation\//i,
      /\/api-documentation\//i,

      // Swagger JSON/YAML patterns
      /swagger.*\.json/i,
      /swagger.*\.yaml/i,
      /swagger.*\.yml/i,

      // Common API spec file patterns
      /\/api\.json/i,
      /\/api\.yaml/i,
      /\/api\.yml/i,
      /\/spec\.json/i,
      /\/spec\.yaml/i,
      /\/spec\.yml/i,

      // Redoc patterns
      /\/redoc\//i,
      /redoc.*\.html/i,

      // Postman documentation patterns
      /documenter\.getpostman\.com/i,
      /postman\.com.*\/documentation\//i,

      // GitBook API docs
      /\.gitbook\.io.*\/api/i,

      // Stoplight patterns
      /\.stoplight\.io/i,

      // Insomnia patterns
      /insomnia\.rest.*\/docs/i,

      // Common hosting platforms
      /readme\.io.*\/reference/i,
      /apiary\.io/i,
      /apiblueprint\.org/i,

      // RAML documentation patterns
      /\.raml$/i,
      /raml.*\/api/i,
      /\/raml\//i,
      /raml\.org/i,

      // API Blueprint patterns
      /\.apib$/i,
      /\.md.*blueprint/i,
      /blueprint.*\/api/i,
      /\/blueprint\//i,
      /apiblueprint/i,

      // AsyncAPI patterns
      /asyncapi/i,
      /\.asyncapi\./i,
      /async-api/i,

      // GraphQL patterns
      /graphql/i,
      /\.graphql$/i,
      /\/graphiql/i,
      /\/playground/i,

      // Version-specific patterns
      /\/v\d+\/swagger/i,
      /\/v\d+\/openapi/i,
      /\/v\d+\/docs/i,
      /\/api\/v\d+\/docs/i,

      // Framework-specific patterns
      /\/rapidoc\//i,
      /\/scalar\//i,
      /\/elements\//i
    ];

    return apiDocPatterns.some(pattern => pattern.test(url));
  }

  // Process API documentation URLs with enhanced format detection and processing
  private async processSwaggerUrl(url: string): Promise<string> {
    try {
      this.logger.debug(`Processing API documentation URL: ${url}`);

      // Check if this is a direct API spec file
      if (this.isDirectApiSpecUrl(url)) {
        return await this.processDirectApiSpec(url);
      }

      // Check if this is a Postman documentation URL
      if (this.isPostmanDocUrl(url)) {
        return await this.processPostmanDoc(url);
      }

      // Check if this is a RAML documentation URL
      if (this.isRamlDocUrl(url)) {
        return await this.processRamlDoc(url);
      }

      // Check if this is an API Blueprint documentation URL
      if (this.isApiBlueprintUrl(url)) {
        return await this.processApiBlueprint(url);
      }

      // Check if this is an AsyncAPI documentation URL
      if (this.isAsyncApiUrl(url)) {
        return await this.processAsyncApi(url);
      }

      // Check if this is a GraphQL documentation URL
      if (this.isGraphQLUrl(url)) {
        return await this.processGraphQL(url);
      }

      // First, try to fetch the HTML to extract API spec URLs
      const response = await fetch(url);
      const contentType = response.headers.get('content-type') || '';

      // If response is JSON/YAML, treat as direct API spec
      if (contentType.includes('application/json') || contentType.includes('application/yaml') || contentType.includes('text/yaml')) {
        const content = await response.text();
        return await this.processApiSpecContent(content, url);
      }

      const html = await response.text();

      // Parse the HTML to find API spec URLs
      const apiUrls = this.extractSwaggerApiUrls(html, url);

      if (apiUrls.length === 0) {
        this.logger.warn(`No API spec URLs found in documentation: ${url}`);
        // Try to extract any embedded JSON/YAML from the HTML
        const embeddedSpec = this.extractEmbeddedApiSpec(html);
        if (embeddedSpec) {
          return await this.processApiSpecContent(embeddedSpec, url);
        }
        // Fallback to cleaning the HTML content
        return this.cleanHtmlContent(html);
      }

      // Fetch all API specifications with enhanced processing
      const apiContents: string[] = [];

      for (const apiUrl of apiUrls) {
        try {
          this.logger.debug(`Fetching API spec from: ${apiUrl}`);
          const apiResponse = await fetch(apiUrl, {
            headers: {
              'User-Agent': 'Mozilla/5.0 (compatible; API-Documentation-Scraper/1.0)',
              'Accept': 'application/json,text/yaml,text/html;q=0.9,*/*;q=0.8'
            }
          });

          if (!apiResponse.ok) {
            this.logger.warn(`Failed to fetch API spec from ${apiUrl}: ${apiResponse.status}`);

            // Try fallback processing for failed requests
            if (apiResponse.status === 404 || apiResponse.status === 403) {
              try {
                const fallbackContent = await this.fallbackProcessing(apiUrl, new Error(`HTTP ${apiResponse.status}`));
                if (fallbackContent && fallbackContent.length > 100) {
                  apiContents.push(fallbackContent);
                }
              } catch (fallbackError) {
                this.logger.debug(`Fallback also failed for ${apiUrl}:`, fallbackError);
              }
            }
            continue;
          }

          const apiContent = await apiResponse.text();
          const processedContent = await this.processApiSpecContent(apiContent, apiUrl);
          if (processedContent) {
            apiContents.push(processedContent);
          }
        } catch (fetchError) {
          this.logger.warn(`Error fetching API spec from ${apiUrl}:`, fetchError);

          // Try fallback processing for network errors
          try {
            const fallbackContent = await this.fallbackProcessing(apiUrl, fetchError as Error);
            if (fallbackContent && fallbackContent.length > 100) {
              apiContents.push(fallbackContent);
            }
          } catch (fallbackError) {
            this.logger.debug(`Fallback processing failed for ${apiUrl}:`, fallbackError);
          }
        }
      }

      if (apiContents.length === 0) {
        this.logger.warn(`No valid API specifications found for ${url}`);
        return this.cleanHtmlContent(html);
      }

      // Combine all API specifications with enhanced metadata
      const combinedContent = this.combineApiSpecifications(apiContents, url);
      this.logger.debug(`Successfully processed API documentation with ${apiContents.length} specs`);
      this.logger.debug(`Combined content length: ${combinedContent.length} characters`);

      return combinedContent;
    } catch (error) {
      this.logger.error(`Error processing API documentation URL ${url}:`, error);
      // Enhanced fallback processing
      return await this.fallbackProcessing(url, error);
    }
  }

  // Helper method to detect direct API specification URLs
  private isDirectApiSpecUrl(url: string): boolean {
    const directSpecPatterns = [
      /\.json$/i,
      /\.yaml$/i,
      /\.yml$/i,
      /\/swagger\.json/i,
      /\/openapi\.json/i,
      /\/api\.json/i,
      /\/spec\.json/i,
      /\/swagger\.yaml/i,
      /\/openapi\.yaml/i,
      /\/api\.yaml/i,
      /\/spec\.yaml/i
    ];
    return directSpecPatterns.some(pattern => pattern.test(url));
  }

  // Helper method to detect Postman documentation URLs
  private isPostmanDocUrl(url: string): boolean {
    return /documenter\.getpostman\.com/i.test(url) || /postman\.com.*\/documentation\//i.test(url);
  }

  // Helper method to detect RAML documentation URLs
  private isRamlDocUrl(url: string): boolean {
    return /\.raml$/i.test(url) || /raml.*\/api/i.test(url) || /\/raml\//i.test(url) || /raml\.org/i.test(url);
  }

  // Helper method to detect API Blueprint URLs
  private isApiBlueprintUrl(url: string): boolean {
    return /\.apib$/i.test(url) || /\.md.*blueprint/i.test(url) || /blueprint.*\/api/i.test(url) || /\/blueprint\//i.test(url) || /apiblueprint/i.test(url);
  }

  // Helper method to detect AsyncAPI URLs
  private isAsyncApiUrl(url: string): boolean {
    return /asyncapi/i.test(url) || /\.asyncapi\./i.test(url) || /async-api/i.test(url);
  }

  // Helper method to detect GraphQL URLs
  private isGraphQLUrl(url: string): boolean {
    return /graphql/i.test(url) || /\.graphql$/i.test(url) || /\/graphiql/i.test(url) || /\/playground/i.test(url);
  }

  // Process direct API specification files
  private async processDirectApiSpec(url: string): Promise<string> {
    try {
      this.logger.debug(`Processing direct API spec: ${url}`);
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Failed to fetch API spec: ${response.status}`);
      }

      const content = await response.text();
      return await this.processApiSpecContent(content, url);
    } catch (error) {
      this.logger.error(`Error processing direct API spec ${url}:`, error);
      throw error;
    }
  }

  // Process Postman documentation
  private async processPostmanDoc(url: string): Promise<string> {
    try {
      this.logger.debug(`Processing Postman documentation: ${url}`);
      const response = await fetch(url);
      const html = await response.text();

      // Try to extract Postman collection data from the page
      const collectionData = this.extractPostmanCollection(html);
      if (collectionData) {
        return this.formatPostmanCollection(collectionData);
      }

      // Fallback to HTML cleaning
      return this.cleanHtmlContent(html);
    } catch (error) {
      this.logger.error(`Error processing Postman documentation ${url}:`, error);
      throw error;
    }
  }

  // Process RAML documentation
  private async processRamlDoc(url: string): Promise<string> {
    try {
      this.logger.debug(`Processing RAML documentation: ${url}`);
      const response = await fetch(url);
      const content = await response.text();

      // Try to parse RAML content
      if (content.trim().startsWith('#%RAML')) {
        return this.formatRamlContent(content, url);
      }

      // If it's HTML, try to extract RAML content
      if (content.includes('<html')) {
        const ramlContent = this.extractRamlFromHtml(content);
        if (ramlContent) {
          return this.formatRamlContent(ramlContent, url);
        }
      }

      // Fallback to HTML cleaning
      return this.cleanHtmlContent(content);
    } catch (error) {
      this.logger.error(`Error processing RAML documentation ${url}:`, error);
      throw error;
    }
  }

  // Process API Blueprint documentation
  private async processApiBlueprint(url: string): Promise<string> {
    try {
      this.logger.debug(`Processing API Blueprint documentation: ${url}`);
      const response = await fetch(url);
      const content = await response.text();

      // Check if it's API Blueprint format
      if (content.includes('FORMAT:') && content.includes('1A') || content.includes('# API Blueprint')) {
        return this.formatApiBlueprintContent(content, url);
      }

      // If it's HTML, try to extract API Blueprint content
      if (content.includes('<html')) {
        const blueprintContent = this.extractApiBlueprintFromHtml(content);
        if (blueprintContent) {
          return this.formatApiBlueprintContent(blueprintContent, url);
        }
      }

      // Fallback to HTML cleaning
      return this.cleanHtmlContent(content);
    } catch (error) {
      this.logger.error(`Error processing API Blueprint documentation ${url}:`, error);
      throw error;
    }
  }

  // Process AsyncAPI documentation
  private async processAsyncApi(url: string): Promise<string> {
    try {
      this.logger.debug(`Processing AsyncAPI documentation: ${url}`);
      const response = await fetch(url);
      const content = await response.text();

      // Try to parse as AsyncAPI JSON/YAML
      try {
        const asyncApiSpec = JSON.parse(content);
        if (asyncApiSpec.asyncapi) {
          return this.formatAsyncApiSpec(asyncApiSpec, url);
        }
      } catch (jsonError) {
        // Try YAML parsing
        try {
          const asyncApiSpec = yaml.load(content);
          if (asyncApiSpec && typeof asyncApiSpec === 'object' && (asyncApiSpec as any).asyncapi) {
            return this.formatAsyncApiSpec(asyncApiSpec, url);
          }
        } catch (yamlError) {
          // Continue to HTML processing
        }
      }

      // If it's HTML, try to extract AsyncAPI content
      if (content.includes('<html')) {
        const asyncApiContent = this.extractAsyncApiFromHtml(content);
        if (asyncApiContent) {
          return this.formatAsyncApiSpec(asyncApiContent, url);
        }
      }

      // Fallback to HTML cleaning
      return this.cleanHtmlContent(content);
    } catch (error) {
      this.logger.error(`Error processing AsyncAPI documentation ${url}:`, error);
      throw error;
    }
  }

  // Process GraphQL documentation
  private async processGraphQL(url: string): Promise<string> {
    try {
      this.logger.debug(`Processing GraphQL documentation: ${url}`);
      const response = await fetch(url);
      const content = await response.text();

      // Check if it's a GraphQL schema file
      if (content.includes('type Query') || content.includes('schema {') || content.includes('type Mutation')) {
        return this.formatGraphQLSchema(content, url);
      }

      // If it's HTML (GraphiQL or playground), try to extract schema
      if (content.includes('<html')) {
        const schemaContent = this.extractGraphQLSchemaFromHtml(content);
        if (schemaContent) {
          return this.formatGraphQLSchema(schemaContent, url);
        }
      }

      // Fallback to HTML cleaning
      return this.cleanHtmlContent(content);
    } catch (error) {
      this.logger.error(`Error processing GraphQL documentation ${url}:`, error);
      throw error;
    }
  }

  // Process API specification content (JSON/YAML)
  private async processApiSpecContent(content: string, sourceUrl: string): Promise<string> {
    try {
      // Try to parse as JSON first
      try {
        const apiJson = JSON.parse(content);
        return this.formatApiSpecification(apiJson, sourceUrl);
      } catch (jsonError) {
        // Try to parse as YAML
        try {
          const apiYaml = yaml.load(content);
          return this.formatApiSpecification(apiYaml, sourceUrl);
        } catch (yamlError) {
          this.logger.warn(`Failed to parse API spec as JSON or YAML from ${sourceUrl}`);
          return content; // Return raw content as fallback
        }
      }
    } catch (error) {
      this.logger.error(`Error processing API spec content from ${sourceUrl}:`, error);
      return content;
    }
  }

  // Extract embedded API specifications from HTML
  private extractEmbeddedApiSpec(html: string): string | null {
    try {
      // Look for JSON embedded in script tags
      const scriptMatches = html.match(/<script[^>]*>([\s\S]*?)<\/script>/gi);
      if (scriptMatches) {
        for (const script of scriptMatches) {
          const content = script.replace(/<\/?script[^>]*>/gi, '');

          // Look for OpenAPI/Swagger patterns in the script content
          if (/["']openapi["']|["']swagger["']|["']info["'].*["']title["']/i.test(content)) {
            // Try to extract JSON object
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              try {
                JSON.parse(jsonMatch[0]);
                return jsonMatch[0];
              } catch (e) {
                // Continue searching
              }
            }
          }
        }
      }

      // Look for YAML in pre tags or code blocks
      const preMatches = html.match(/<pre[^>]*>([\s\S]*?)<\/pre>/gi);
      if (preMatches) {
        for (const pre of preMatches) {
          const content = pre.replace(/<\/?pre[^>]*>/gi, '').trim();
          if (/^openapi:|^swagger:/i.test(content)) {
            return content;
          }
        }
      }

      return null;
    } catch (error) {
      this.logger.warn('Error extracting embedded API spec:', error);
      return null;
    }
  }

  // Combine multiple API specifications with metadata and optimization
  private combineApiSpecifications(apiContents: string[], sourceUrl: string): string {
    const parts: string[] = [];

    parts.push(`=== API Documentation from ${sourceUrl} ===`);
    parts.push(`Source: ${sourceUrl}`);
    parts.push(`Processed: ${new Date().toISOString()}`);
    parts.push(`Number of specifications: ${apiContents.length}`);
    parts.push('');

    // Optimize and combine content
    const optimizedContents = apiContents.map((content, index) => {
      const optimized = this.optimizeContentForEmbedding(content, sourceUrl);
      return optimized;
    });

    // Add summary section for better retrieval
    const summary = this.generateContentSummary(optimizedContents, sourceUrl);
    parts.push(summary);
    parts.push('');

    optimizedContents.forEach((content, index) => {
      if (optimizedContents.length > 1) {
        parts.push(`--- API Specification ${index + 1} ---`);
      }
      parts.push(content);
      if (index < optimizedContents.length - 1) {
        parts.push('\n---\n');
      }
    });

    return parts.join('\n');
  }

  // Optimize content for better embedding and retrieval performance
  private optimizeContentForEmbedding(content: string, sourceUrl: string): string {
    // Extract key information for better searchability
    const optimizedParts: string[] = [];

    // Add searchable keywords at the beginning
    const keywords = this.extractKeywords(content);
    if (keywords.length > 0) {
      optimizedParts.push(`Keywords: ${keywords.join(', ')}`);
      optimizedParts.push('');
    }

    // Process content in chunks for better embedding
    const chunks = this.splitContentIntoChunks(content);
    const processedChunks = chunks.map(chunk => this.enhanceChunkForRetrieval(chunk, sourceUrl));

    optimizedParts.push(...processedChunks);

    return optimizedParts.join('\n');
  }

  // Extract keywords for better searchability
  private extractKeywords(content: string): string[] {
    const keywords = new Set<string>();

    // Extract API-related keywords
    const apiKeywordPatterns = [
      /API:\s*([^\\n]+)/gi,
      /Title:\s*([^\\n]+)/gi,
      /Description:\s*([^\\n]+)/gi,
      /Tag[s]?:\s*([^\\n]+)/gi,
      /(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)\s+([^\\s\\n]+)/gi,
      /\/[a-zA-Z0-9\/_-]+/g, // API paths
      /\b[A-Z][a-zA-Z0-9]*(?:API|Service|Controller|Resource)\b/g // Service names
    ];

    for (const pattern of apiKeywordPatterns) {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const cleaned = match.replace(/^(API|Title|Description|Tags?):\s*/i, '').trim();
          if (cleaned.length > 2 && cleaned.length < 50) {
            keywords.add(cleaned);
          }
        });
      }
    }

    return Array.from(keywords).slice(0, 20); // Limit to top 20 keywords
  }

  // Split content into manageable chunks for embedding
  private splitContentIntoChunks(content: string, maxChunkSize: number = 1500): string[] {
    const lines = content.split('\n');
    const chunks: string[] = [];
    let currentChunk: string[] = [];
    let currentSize = 0;

    for (const line of lines) {
      const lineSize = line.length + 1; // +1 for newline

      if (currentSize + lineSize > maxChunkSize && currentChunk.length > 0) {
        chunks.push(currentChunk.join('\n'));
        currentChunk = [line];
        currentSize = lineSize;
      } else {
        currentChunk.push(line);
        currentSize += lineSize;
      }
    }

    if (currentChunk.length > 0) {
      chunks.push(currentChunk.join('\n'));
    }

    return chunks;
  }

  // Enhance individual chunks for better retrieval
  private enhanceChunkForRetrieval(chunk: string, sourceUrl: string): string {
    const lines = chunk.split('\n');
    const enhancedLines: string[] = [];

    for (const line of lines) {
      const trimmed = line.trim();

      // Add context to endpoint definitions
      if (trimmed.match(/^(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)\s+/i)) {
        const method = trimmed.split(' ')[0].toUpperCase();
        const path = trimmed.split(' ')[1] || '';
        enhancedLines.push(`${line} [${method} endpoint for ${this.inferEndpointPurpose(path)}]`);
      }
      // Add context to schema definitions
      else if (trimmed.startsWith('=== ') && trimmed.includes('Schema')) {
        enhancedLines.push(`${line} [Data model definition]`);
      }
      // Add context to response codes
      else if (trimmed.match(/^\d{3}:/)) {
        const code = trimmed.split(':')[0];
        const description = this.getHttpStatusDescription(parseInt(code));
        enhancedLines.push(`${line} [${description}]`);
      }
      else {
        enhancedLines.push(line);
      }
    }

    return enhancedLines.join('\n');
  }

  // Generate content summary for better retrieval
  private generateContentSummary(contents: string[], sourceUrl: string): string {
    const summaryParts: string[] = [];

    summaryParts.push('=== API Documentation Summary ===');

    // Extract API information
    const apiInfo = this.extractApiInfo(contents);
    if (apiInfo.title) summaryParts.push(`API: ${apiInfo.title}`);
    if (apiInfo.version) summaryParts.push(`Version: ${apiInfo.version}`);
    if (apiInfo.description) summaryParts.push(`Description: ${apiInfo.description}`);

    // Extract endpoint summary
    const endpoints = this.extractEndpointSummary(contents);
    if (endpoints.length > 0) {
      summaryParts.push(`\nAvailable Endpoints (${endpoints.length} total):`);
      endpoints.slice(0, 10).forEach(endpoint => { // Show top 10 endpoints
        summaryParts.push(`- ${endpoint}`);
      });
      if (endpoints.length > 10) {
        summaryParts.push(`... and ${endpoints.length - 10} more endpoints`);
      }
    }

    // Extract schema summary
    const schemas = this.extractSchemaSummary(contents);
    if (schemas.length > 0) {
      summaryParts.push(`\nData Models (${schemas.length} total):`);
      schemas.slice(0, 5).forEach(schema => { // Show top 5 schemas
        summaryParts.push(`- ${schema}`);
      });
      if (schemas.length > 5) {
        summaryParts.push(`... and ${schemas.length - 5} more models`);
      }
    }

    return summaryParts.join('\n');
  }

  // Format RAML content
  private formatRamlContent(content: string, sourceUrl: string): string {
    const parts: string[] = [];

    parts.push(`=== RAML API Documentation ===`);
    parts.push(`Source: ${sourceUrl}`);

    const lines = content.split('\n');
    let currentSection = '';

    for (const line of lines) {
      const trimmed = line.trim();

      if (trimmed.startsWith('#%RAML')) {
        parts.push(`RAML Version: ${trimmed.replace('#%RAML', '').trim()}`);
      } else if (trimmed.startsWith('title:')) {
        parts.push(`Title: ${trimmed.replace('title:', '').trim()}`);
      } else if (trimmed.startsWith('version:')) {
        parts.push(`Version: ${trimmed.replace('version:', '').trim()}`);
      } else if (trimmed.startsWith('baseUri:')) {
        parts.push(`Base URI: ${trimmed.replace('baseUri:', '').trim()}`);
      } else if (trimmed.startsWith('description:')) {
        parts.push(`Description: ${trimmed.replace('description:', '').trim()}`);
      } else if (trimmed.match(/^\/\w+/)) {
        // Resource path
        currentSection = 'endpoints';
        if (currentSection !== 'endpoints') {
          parts.push('\n=== API Endpoints ===');
        }
        parts.push(`\n${trimmed}:`);
      } else if (trimmed.match(/^\s+(get|post|put|delete|patch|head|options):/i)) {
        // HTTP method
        parts.push(`  ${trimmed}`);
      }
    }

    return parts.join('\n');
  }

  // Extract RAML content from HTML
  private extractRamlFromHtml(html: string): string | null {
    // Look for RAML content in script tags or pre blocks
    const ramlPatterns = [
      /<pre[^>]*>([\s\S]*?#%RAML[\s\S]*?)<\/pre>/gi,
      /<code[^>]*>([\s\S]*?#%RAML[\s\S]*?)<\/code>/gi,
      /<script[^>]*>([\s\S]*?#%RAML[\s\S]*?)<\/script>/gi
    ];

    for (const pattern of ramlPatterns) {
      const match = pattern.exec(html);
      if (match && match[1]) {
        return match[1].replace(/<[^>]*>/g, '').trim();
      }
    }

    return null;
  }

  // Format API Blueprint content
  private formatApiBlueprintContent(content: string, sourceUrl: string): string {
    const parts: string[] = [];

    parts.push(`=== API Blueprint Documentation ===`);
    parts.push(`Source: ${sourceUrl}`);

    const lines = content.split('\n');
    let inGroup = false;

    for (const line of lines) {
      const trimmed = line.trim();

      if (trimmed.startsWith('FORMAT:')) {
        parts.push(`Format: ${trimmed.replace('FORMAT:', '').trim()}`);
      } else if (trimmed.startsWith('# ') && !trimmed.includes('##')) {
        // Main title
        parts.push(`Title: ${trimmed.replace('#', '').trim()}`);
      } else if (trimmed.startsWith('## ')) {
        // Section header
        if (trimmed.includes('Group')) {
          inGroup = true;
          parts.push(`\n=== ${trimmed.replace('##', '').trim()} ===`);
        } else {
          parts.push(`\n${trimmed.replace('##', '').trim()}`);
        }
      } else if (trimmed.startsWith('### ')) {
        // Resource or action
        parts.push(`  ${trimmed.replace('###', '').trim()}`);
      } else if (trimmed.match(/^\+\s+(Request|Response)/)) {
        // Request/Response
        parts.push(`    ${trimmed}`);
      }
    }

    return parts.join('\n');
  }

  // Extract API Blueprint content from HTML
  private extractApiBlueprintFromHtml(html: string): string | null {
    // Look for API Blueprint content in script tags or pre blocks
    const blueprintPatterns = [
      /<pre[^>]*>([\s\S]*?FORMAT:[\s\S]*?)<\/pre>/gi,
      /<code[^>]*>([\s\S]*?FORMAT:[\s\S]*?)<\/code>/gi,
      /<pre[^>]*>([\s\S]*?# API Blueprint[\s\S]*?)<\/pre>/gi
    ];

    for (const pattern of blueprintPatterns) {
      const match = pattern.exec(html);
      if (match && match[1]) {
        return match[1].replace(/<[^>]*>/g, '').trim();
      }
    }

    return null;
  }

  // Format AsyncAPI specification
  private formatAsyncApiSpec(spec: any, sourceUrl: string): string {
    const parts: string[] = [];

    parts.push(`=== AsyncAPI Specification ===`);
    parts.push(`Source: ${sourceUrl}`);

    if (spec.asyncapi) {
      parts.push(`AsyncAPI Version: ${spec.asyncapi}`);
    }

    if (spec.info) {
      if (spec.info.title) parts.push(`Title: ${spec.info.title}`);
      if (spec.info.description) parts.push(`Description: ${spec.info.description}`);
      if (spec.info.version) parts.push(`Version: ${spec.info.version}`);
    }

    if (spec.servers) {
      parts.push('\n=== Servers ===');
      Object.entries(spec.servers).forEach(([name, server]: [string, any]) => {
        parts.push(`${name}: ${server.url}`);
        if (server.description) parts.push(`  Description: ${server.description}`);
        if (server.protocol) parts.push(`  Protocol: ${server.protocol}`);
      });
    }

    if (spec.channels) {
      parts.push('\n=== Channels ===');
      Object.entries(spec.channels).forEach(([channel, channelObj]: [string, any]) => {
        parts.push(`\n${channel}:`);
        if (channelObj.description) parts.push(`  Description: ${channelObj.description}`);

        if (channelObj.subscribe) {
          parts.push(`  Subscribe:`);
          if (channelObj.subscribe.summary) parts.push(`    Summary: ${channelObj.subscribe.summary}`);
          if (channelObj.subscribe.description) parts.push(`    Description: ${channelObj.subscribe.description}`);
        }

        if (channelObj.publish) {
          parts.push(`  Publish:`);
          if (channelObj.publish.summary) parts.push(`    Summary: ${channelObj.publish.summary}`);
          if (channelObj.publish.description) parts.push(`    Description: ${channelObj.publish.description}`);
        }
      });
    }

    return parts.join('\n');
  }

  // Extract AsyncAPI content from HTML
  private extractAsyncApiFromHtml(html: string): any {
    try {
      // Look for AsyncAPI JSON in script tags
      const scriptMatches = html.match(/<script[^>]*>([\s\S]*?)<\/script>/gi);
      if (scriptMatches) {
        for (const script of scriptMatches) {
          const content = script.replace(/<\/?script[^>]*>/gi, '');
          if (/["']asyncapi["']/i.test(content)) {
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              try {
                const parsed = JSON.parse(jsonMatch[0]);
                if (parsed.asyncapi) return parsed;
              } catch (e) {
                // Continue searching
              }
            }
          }
        }
      }
      return null;
    } catch (error) {
      this.logger.warn('Error extracting AsyncAPI from HTML:', error);
      return null;
    }
  }

  // Format GraphQL schema
  private formatGraphQLSchema(schema: string, sourceUrl: string): string {
    const parts: string[] = [];

    parts.push(`=== GraphQL Schema ===`);
    parts.push(`Source: ${sourceUrl}`);

    const lines = schema.split('\n');
    let currentType = '';

    for (const line of lines) {
      const trimmed = line.trim();

      if (trimmed.startsWith('type ') && !trimmed.includes('{')) {
        currentType = 'type';
        parts.push(`\n${trimmed}`);
      } else if (trimmed.startsWith('interface ')) {
        currentType = 'interface';
        parts.push(`\n${trimmed}`);
      } else if (trimmed.startsWith('enum ')) {
        currentType = 'enum';
        parts.push(`\n${trimmed}`);
      } else if (trimmed.startsWith('input ')) {
        currentType = 'input';
        parts.push(`\n${trimmed}`);
      } else if (trimmed.startsWith('schema {')) {
        parts.push(`\n=== Schema Definition ===`);
        parts.push(trimmed);
      } else if (trimmed && !trimmed.startsWith('#') && !trimmed.startsWith('"""')) {
        parts.push(`  ${trimmed}`);
      }
    }

    return parts.join('\n');
  }

  // Extract GraphQL schema from HTML
  private extractGraphQLSchemaFromHtml(html: string): string | null {
    // Look for GraphQL schema in script tags or pre blocks
    const graphqlPatterns = [
      /<pre[^>]*>([\s\S]*?type Query[\s\S]*?)<\/pre>/gi,
      /<code[^>]*>([\s\S]*?type Query[\s\S]*?)<\/code>/gi,
      /<script[^>]*>([\s\S]*?type Query[\s\S]*?)<\/script>/gi,
      /<pre[^>]*>([\s\S]*?schema \{[\s\S]*?)<\/pre>/gi
    ];

    for (const pattern of graphqlPatterns) {
      const match = pattern.exec(html);
      if (match && match[1]) {
        return match[1].replace(/<[^>]*>/g, '').trim();
      }
    }

    return null;
  }

  // Enhanced fallback processing with multiple strategies
  private async fallbackProcessing(url: string, originalError: Error): Promise<string> {
    this.logger.warn(`Attempting fallback processing for ${url} due to error: ${originalError.message}`);

    const fallbackStrategies = [
      () => this.tryWithDifferentHeaders(url),
      () => this.tryWithSimplifiedUrl(url),
      () => this.tryCommonApiPaths(url),
      () => this.tryWaybackMachine(url),
      () => this.tryAlternativeFormats(url)
    ];

    for (let i = 0; i < fallbackStrategies.length; i++) {
      try {
        this.logger.debug(`Trying fallback strategy ${i + 1} for ${url}`);
        const result = await fallbackStrategies[i]();
        if (result && result.length > 100) { // Minimum content threshold
          this.logger.log(`Fallback strategy ${i + 1} succeeded for ${url}`);
          return result;
        }
      } catch (strategyError) {
        this.logger.debug(`Fallback strategy ${i + 1} failed for ${url}:`, strategyError);
        continue;
      }
    }

    // If all strategies fail, return a meaningful error message
    return this.generateErrorFallback(url, originalError);
  }

  // Try fetching with different headers
  private async tryWithDifferentHeaders(url: string): Promise<string> {
    const headerSets = [
      {
        'User-Agent': 'Mozilla/5.0 (compatible; API-Documentation-Scraper/1.0)',
        'Accept': 'text/html,application/xhtml+xml,application/xml,application/json,text/yaml;q=0.9,*/*;q=0.8'
      },
      {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json,text/yaml,text/html;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5'
      },
      {
        'User-Agent': 'curl/7.68.0',
        'Accept': '*/*'
      }
    ];

    for (const headers of headerSets) {
      try {
        const response = await fetch(url, { headers });
        if (response.ok) {
          const content = await response.text();
          const contentType = response.headers.get('content-type') || '';

          if (contentType.includes('application/json')) {
            try {
              const json = JSON.parse(content);
              return this.formatApiSpecification(json, url);
            } catch (e) {
              // Continue to next processing
            }
          }

          if (contentType.includes('yaml') || contentType.includes('yml')) {
            try {
              const yamlData = yaml.load(content);
              return this.formatApiSpecification(yamlData, url);
            } catch (e) {
              // Continue to next processing
            }
          }

          return this.cleanHtmlContent(content);
        }
      } catch (error) {
        continue;
      }
    }

    throw new Error('All header strategies failed');
  }

  // Try simplified URL variations
  private async tryWithSimplifiedUrl(url: string): Promise<string> {
    const urlObj = new URL(url);
    const simplifiedUrls = [
      `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`,
      `${urlObj.protocol}//${urlObj.host}`,
      `${urlObj.protocol}//${urlObj.host}/api-docs`,
      `${urlObj.protocol}//${urlObj.host}/swagger.json`,
      `${urlObj.protocol}//${urlObj.host}/openapi.json`
    ];

    for (const simplifiedUrl of simplifiedUrls) {
      try {
        const response = await fetch(simplifiedUrl);
        if (response.ok) {
          const content = await response.text();
          return await this.processApiSpecContent(content, simplifiedUrl);
        }
      } catch (error) {
        continue;
      }
    }

    throw new Error('Simplified URL strategies failed');
  }

  // Try common API documentation paths
  private async tryCommonApiPaths(url: string): Promise<string> {
    const urlObj = new URL(url);
    const commonPaths = [
      '/swagger.json', '/openapi.json', '/api-docs', '/docs/swagger.json',
      '/v1/swagger.json', '/v2/swagger.json', '/v3/swagger.json',
      '/api/swagger.json', '/api/openapi.json', '/api/docs'
    ];

    for (const path of commonPaths) {
      try {
        const testUrl = `${urlObj.protocol}//${urlObj.host}${path}`;
        const response = await fetch(testUrl);
        if (response.ok) {
          const content = await response.text();
          return await this.processApiSpecContent(content, testUrl);
        }
      } catch (error) {
        continue;
      }
    }

    throw new Error('Common API paths failed');
  }

  // Try Wayback Machine as last resort
  private async tryWaybackMachine(url: string): Promise<string> {
    try {
      const waybackUrl = `https://web.archive.org/web/timemap/json?url=${encodeURIComponent(url)}&limit=1`;
      const response = await fetch(waybackUrl);
      if (response.ok) {
        const data = await response.json();
        if (data.length > 1) {
          const archivedUrl = data[1][1]; // Get the archived URL
          const archivedResponse = await fetch(archivedUrl);
          if (archivedResponse.ok) {
            const content = await archivedResponse.text();
            return this.cleanHtmlContent(content);
          }
        }
      }
    } catch (error) {
      // Wayback Machine is optional, don't throw
    }

    throw new Error('Wayback Machine strategy failed');
  }

  // Try alternative formats
  private async tryAlternativeFormats(url: string): Promise<string> {
    const urlObj = new URL(url);
    const alternatives = [
      url.replace('.json', '.yaml'),
      url.replace('.yaml', '.json'),
      url.replace('/swagger/', '/openapi/'),
      url.replace('/openapi/', '/swagger/'),
      url.replace('/docs/', '/api-docs/'),
      url.replace('/api-docs/', '/docs/')
    ];

    for (const altUrl of alternatives) {
      if (altUrl !== url) {
        try {
          const response = await fetch(altUrl);
          if (response.ok) {
            const content = await response.text();
            return await this.processApiSpecContent(content, altUrl);
          }
        } catch (error) {
          continue;
        }
      }
    }

    throw new Error('Alternative format strategies failed');
  }

  // Generate meaningful error fallback content
  private generateErrorFallback(url: string, originalError: Error): string {
    const parts: string[] = [];

    parts.push(`=== API Documentation Processing Error ===`);
    parts.push(`Source URL: ${url}`);
    parts.push(`Error: ${originalError.message}`);
    parts.push(`Timestamp: ${new Date().toISOString()}`);
    parts.push('');
    parts.push('The API documentation could not be processed due to the following issues:');

    if (originalError.message.includes('fetch')) {
      parts.push('- Network connectivity issues or invalid URL');
      parts.push('- The API documentation server may be temporarily unavailable');
      parts.push('- CORS restrictions may be preventing access');
    } else if (originalError.message.includes('parse')) {
      parts.push('- The API specification format is invalid or corrupted');
      parts.push('- The content may not be a valid OpenAPI/Swagger specification');
    } else {
      parts.push('- Unknown processing error occurred');
    }

    parts.push('');
    parts.push('Suggested actions:');
    parts.push('- Verify the URL is accessible and returns valid API documentation');
    parts.push('- Check if the API documentation follows OpenAPI/Swagger standards');
    parts.push('- Try accessing the documentation manually to verify its availability');

    return parts.join('\n');
  }

  // Helper method to infer endpoint purpose from path
  private inferEndpointPurpose(path: string): string {
    if (!path) return 'unknown operation';

    const pathLower = path.toLowerCase();

    if (pathLower.includes('/auth') || pathLower.includes('/login')) return 'authentication';
    if (pathLower.includes('/user') || pathLower.includes('/profile')) return 'user management';
    if (pathLower.includes('/search')) return 'search operations';
    if (pathLower.includes('/upload') || pathLower.includes('/file')) return 'file operations';
    if (pathLower.includes('/order') || pathLower.includes('/purchase')) return 'order management';
    if (pathLower.includes('/payment') || pathLower.includes('/billing')) return 'payment processing';
    if (pathLower.includes('/admin')) return 'administrative operations';
    if (pathLower.includes('/report') || pathLower.includes('/analytics')) return 'reporting and analytics';
    if (pathLower.includes('/config') || pathLower.includes('/setting')) return 'configuration';
    if (pathLower.includes('/health') || pathLower.includes('/status')) return 'health monitoring';

    // Extract resource name from path
    const segments = path.split('/').filter(s => s && !s.match(/^\{.*\}$/));
    if (segments.length > 0) {
      const resource = segments[segments.length - 1];
      return `${resource} operations`;
    }

    return 'API operations';
  }

  // Helper method to get HTTP status code descriptions
  private getHttpStatusDescription(code: number): string {
    const statusDescriptions: { [key: number]: string } = {
      200: 'Success',
      201: 'Created successfully',
      202: 'Accepted for processing',
      204: 'No content',
      400: 'Bad request',
      401: 'Unauthorized',
      403: 'Forbidden',
      404: 'Not found',
      405: 'Method not allowed',
      409: 'Conflict',
      422: 'Validation error',
      429: 'Rate limit exceeded',
      500: 'Internal server error',
      502: 'Bad gateway',
      503: 'Service unavailable'
    };

    return statusDescriptions[code] || `HTTP ${code}`;
  }

  // Extract API information from content
  private extractApiInfo(contents: string[]): { title?: string; version?: string; description?: string } {
    const info: { title?: string; version?: string; description?: string } = {};

    for (const content of contents) {
      const lines = content.split('\n');

      for (const line of lines) {
        const trimmed = line.trim();

        if (trimmed.startsWith('API:') && !info.title) {
          info.title = trimmed.replace('API:', '').trim();
        } else if (trimmed.startsWith('Title:') && !info.title) {
          info.title = trimmed.replace('Title:', '').trim();
        } else if (trimmed.startsWith('Version:') && !info.version) {
          info.version = trimmed.replace('Version:', '').trim();
        } else if (trimmed.startsWith('Description:') && !info.description) {
          info.description = trimmed.replace('Description:', '').trim();
        }
      }
    }

    return info;
  }

  // Extract endpoint summary from content
  private extractEndpointSummary(contents: string[]): string[] {
    const endpoints: string[] = [];

    for (const content of contents) {
      const lines = content.split('\n');

      for (const line of lines) {
        const trimmed = line.trim();
        const endpointMatch = trimmed.match(/^(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)\s+([^\s]+)/i);

        if (endpointMatch) {
          const method = endpointMatch[1].toUpperCase();
          const path = endpointMatch[2];
          endpoints.push(`${method} ${path}`);
        }
      }
    }

    return [...new Set(endpoints)]; // Remove duplicates
  }

  // Extract schema summary from content
  private extractSchemaSummary(contents: string[]): string[] {
    const schemas: string[] = [];

    for (const content of contents) {
      const lines = content.split('\n');

      for (const line of lines) {
        const trimmed = line.trim();

        if (trimmed.startsWith('=== ') && trimmed.includes('Schema')) {
          const schemaName = trimmed.replace(/^=== /, '').replace(' Schema ===', '').trim();
          if (schemaName && !schemas.includes(schemaName)) {
            schemas.push(schemaName);
          }
        }
      }
    }

    return schemas;
  }

  // Extract Postman collection data from HTML
  private extractPostmanCollection(html: string): any {
    try {
      // Look for Postman collection data in script tags
      const scriptMatches = html.match(/<script[^>]*>([\s\S]*?)<\/script>/gi);
      if (scriptMatches) {
        for (const script of scriptMatches) {
          const content = script.replace(/<\/?script[^>]*>/gi, '');

          // Look for collection data patterns
          if (/collection|postman/i.test(content)) {
            const jsonMatch = content.match(/\{[\s\S]*"info"[\s\S]*"name"[\s\S]*\}/);
            if (jsonMatch) {
              try {
                return JSON.parse(jsonMatch[0]);
              } catch (e) {
                // Continue searching
              }
            }
          }
        }
      }
      return null;
    } catch (error) {
      this.logger.warn('Error extracting Postman collection:', error);
      return null;
    }
  }

  // Format Postman collection data
  private formatPostmanCollection(collection: any): string {
    try {
      const parts: string[] = [];

      if (collection.info) {
        parts.push(`=== Postman Collection: ${collection.info.name || 'Unknown'} ===`);
        if (collection.info.description) {
          parts.push(`Description: ${collection.info.description}`);
        }
        if (collection.info.version) {
          parts.push(`Version: ${collection.info.version}`);
        }
      }

      if (collection.item && Array.isArray(collection.item)) {
        parts.push('\n=== API Endpoints ===');
        this.formatPostmanItems(collection.item, parts, 0);
      }

      return parts.join('\n');
    } catch (error) {
      this.logger.warn('Error formatting Postman collection:', error);
      return JSON.stringify(collection, null, 2);
    }
  }

  // Helper to format Postman items recursively
  private formatPostmanItems(items: any[], parts: string[], depth: number): void {
    const indent = '  '.repeat(depth);

    items.forEach(item => {
      if (item.item && Array.isArray(item.item)) {
        // This is a folder
        parts.push(`${indent}📁 ${item.name || 'Folder'}`);
        if (item.description) {
          parts.push(`${indent}   ${item.description}`);
        }
        this.formatPostmanItems(item.item, parts, depth + 1);
      } else if (item.request) {
        // This is a request
        const method = item.request.method || 'GET';
        const url = this.extractPostmanUrl(item.request.url);
        parts.push(`${indent}${method} ${url}`);

        if (item.name) {
          parts.push(`${indent}  Name: ${item.name}`);
        }

        if (item.request.description) {
          parts.push(`${indent}  Description: ${item.request.description}`);
        }

        // Add headers
        if (item.request.header && Array.isArray(item.request.header)) {
          const headers = item.request.header.filter((h: any) => !h.disabled);
          if (headers.length > 0) {
            parts.push(`${indent}  Headers:`);
            headers.forEach((header: any) => {
              parts.push(`${indent}    ${header.key}: ${header.value}`);
            });
          }
        }

        // Add body
        if (item.request.body) {
          parts.push(`${indent}  Request Body:`);
          if (item.request.body.mode === 'raw') {
            parts.push(`${indent}    ${item.request.body.raw}`);
          } else if (item.request.body.mode === 'formdata') {
            parts.push(`${indent}    Form Data:`);
            item.request.body.formdata?.forEach((field: any) => {
              parts.push(`${indent}      ${field.key}: ${field.value}`);
            });
          }
        }
      }
    });
  }

  // Extract URL from Postman URL object
  private extractPostmanUrl(urlObj: any): string {
    if (typeof urlObj === 'string') {
      return urlObj;
    }

    if (urlObj.raw) {
      return urlObj.raw;
    }

    if (urlObj.protocol && urlObj.host && urlObj.path) {
      const protocol = Array.isArray(urlObj.protocol) ? urlObj.protocol.join('') : urlObj.protocol;
      const host = Array.isArray(urlObj.host) ? urlObj.host.join('.') : urlObj.host;
      const path = Array.isArray(urlObj.path) ? '/' + urlObj.path.join('/') : urlObj.path;
      return `${protocol}://${host}${path}`;
    }

    return 'Unknown URL';
  }

  // Enhanced API specification formatter
  private formatApiSpecification(apiSpec: any, sourceUrl: string): string {
    try {
      // Detect the API specification format
      if (apiSpec.openapi) {
        return this.formatOpenApiSpec(apiSpec, sourceUrl);
      } else if (apiSpec.swagger) {
        return this.formatSwaggerJson(apiSpec);
      } else if (apiSpec.info && (apiSpec.paths || apiSpec.item)) {
        // Could be OpenAPI without version or Postman collection
        if (apiSpec.item) {
          return this.formatPostmanCollection(apiSpec);
        } else {
          return this.formatOpenApiSpec(apiSpec, sourceUrl);
        }
      } else {
        // Unknown format, try to extract useful information
        return this.formatGenericApiSpec(apiSpec, sourceUrl);
      }
    } catch (error) {
      this.logger.warn(`Error formatting API specification from ${sourceUrl}:`, error);
      return JSON.stringify(apiSpec, null, 2);
    }
  }

  // Format OpenAPI 3.x specifications
  private formatOpenApiSpec(spec: any, sourceUrl: string): string {
    const parts: string[] = [];

    parts.push(`=== OpenAPI Specification ===`);
    parts.push(`Source: ${sourceUrl}`);

    if (spec.openapi) {
      parts.push(`OpenAPI Version: ${spec.openapi}`);
    }

    // Use existing formatSwaggerJson method as it handles OpenAPI well
    const formattedContent = this.formatSwaggerJson(spec);
    parts.push(formattedContent);

    return parts.join('\n');
  }

  // Format generic API specifications
  private formatGenericApiSpec(spec: any, sourceUrl: string): string {
    const parts: string[] = [];

    parts.push(`=== API Specification ===`);
    parts.push(`Source: ${sourceUrl}`);

    if (spec.info) {
      if (spec.info.title) parts.push(`Title: ${spec.info.title}`);
      if (spec.info.description) parts.push(`Description: ${spec.info.description}`);
      if (spec.info.version) parts.push(`Version: ${spec.info.version}`);
    }

    // Try to extract endpoints
    if (spec.paths) {
      parts.push('\n=== Endpoints ===');
      Object.entries(spec.paths).forEach(([path, methods]: [string, any]) => {
        Object.entries(methods).forEach(([method, operation]: [string, any]) => {
          if (typeof operation === 'object' && operation !== null) {
            parts.push(`${method.toUpperCase()} ${path}`);
            if (operation.summary) parts.push(`  Summary: ${operation.summary}`);
            if (operation.description) parts.push(`  Description: ${operation.description}`);
          }
        });
      });
    }

    // If no structured data found, return JSON
    if (parts.length <= 3) {
      parts.push('\n=== Raw Specification ===');
      parts.push(JSON.stringify(spec, null, 2));
    }

    return parts.join('\n');
  }

  // Extract API specification URLs from API documentation HTML
  private extractSwaggerApiUrls(html: string, baseUrl: string): string[] {
    const urls: string[] = [];

    try {
      // Parse the base URL to get the origin and path
      const urlObj = new URL(baseUrl);
      const baseOrigin = urlObj.origin;
      const basePath = urlObj.pathname.replace(/\/[^\/]*$/, ''); // Remove filename

      // Enhanced patterns for different API documentation tools
      const patterns = [
        // Swagger UI patterns
        /urls:\s*\[\s*([^\]]+)\]/g,
        /url:\s*["']([^"']+)["']/g,
        /spec-url=["']([^"']+)["']/g,
        /data-spec-url=["']([^"']+)["']/g,
        /configUrl:\s*["']([^"']+)["']/g,

        // OpenAPI patterns
        /openapi:\s*["']([^"']+)["']/g,
        /openApiUrl:\s*["']([^"']+)["']/g,
        /specUrl:\s*["']([^"']+)["']/g,

        // Redoc patterns
        /spec-url=["']([^"']+)["']/g,
        /data-spec=["']([^"']+)["']/g,

        // Generic API spec patterns
        /apiSpec:\s*["']([^"']+)["']/g,
        /apiUrl:\s*["']([^"']+)["']/g,
        /schemaUrl:\s*["']([^"']+)["']/g,

        // JSON-LD and meta tag patterns
        /<link[^>]+href=["']([^"']*(?:swagger|openapi|api)[^"']*)["']/gi,
        /<meta[^>]+content=["']([^"']*(?:swagger|openapi|api)[^"']*)["']/gi,

        // Script src patterns
        /<script[^>]+src=["']([^"']*(?:swagger|openapi|api)[^"']*)["']/gi,

        // Postman collection patterns
        /collection:\s*["']([^"']+)["']/g,
        /postman_collection:\s*["']([^"']+)["']/g,
      ];

      for (const pattern of patterns) {
        let match: RegExpExecArray | null;
        while ((match = pattern.exec(html)) !== null) {
          if (match[1]) {
            // Handle urls array format
            if (match[0].includes('urls:')) {
              const urlsContent = match[1];
              const urlMatches = urlsContent.match(/url:\s*["']([^"']+)["']/g);
              if (urlMatches) {
                for (const urlMatch of urlMatches) {
                  const urlValue = urlMatch.match(/["']([^"']+)["']/)?.[1];
                  if (urlValue) {
                    urls.push(this.resolveUrl(urlValue, baseOrigin, basePath));
                  }
                }
              }
            } else {
              // Handle single url format
              urls.push(this.resolveUrl(match[1], baseOrigin, basePath));
            }
          }
        }
      }

      // If no URLs found, try comprehensive common default paths
      if (urls.length === 0) {
        const commonPaths = [
          // Standard Swagger/OpenAPI paths
          '/swagger.json',
          '/swagger.yaml',
          '/swagger.yml',
          '/openapi.json',
          '/openapi.yaml',
          '/openapi.yml',
          '/api-docs',
          '/api-docs.json',
          '/api-docs.yaml',

          // Versioned API paths
          '/v1/swagger.json',
          '/v2/swagger.json',
          '/v3/swagger.json',
          '/v1/openapi.json',
          '/v2/openapi.json',
          '/v3/openapi.json',
          '/api/v1/swagger.json',
          '/api/v2/swagger.json',
          '/api/v3/swagger.json',
          '/api/v1/openapi.json',
          '/api/v2/openapi.json',
          '/api/v3/openapi.json',

          // Documentation paths
          '/docs/swagger.json',
          '/docs/openapi.json',
          '/docs/api.json',
          '/documentation/swagger.json',
          '/documentation/openapi.json',

          // Framework-specific paths
          '/swagger/v1',
          '/swagger/v2',
          '/swagger/v3',
          '/api/swagger/v1',
          '/api/swagger/v2',
          '/api/swagger/v3',

          // Generic API spec paths
          '/api.json',
          '/api.yaml',
          '/api.yml',
          '/spec.json',
          '/spec.yaml',
          '/spec.yml',
          '/schema.json',
          '/schema.yaml',

          // Alternative common paths
          '/rest/swagger.json',
          '/rest/openapi.json',
          '/public/swagger.json',
          '/public/openapi.json',
          '/static/swagger.json',
          '/static/openapi.json',

          // Platform-specific paths
          '/apidoc.json',
          '/api-spec.json',
          '/openapi-spec.json',
          '/swagger-spec.json'
        ];

        for (const path of commonPaths) {
          urls.push(baseOrigin + path);
        }
      }

      // Remove duplicates
      return [...new Set(urls)];
    } catch (error) {
      this.logger.warn(`Error extracting Swagger API URLs from ${baseUrl}:`, error);
      return [];
    }
  }

  // Resolve relative URLs to absolute URLs
  private resolveUrl(url: string, baseOrigin: string, basePath: string): string {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    if (url.startsWith('/')) {
      return baseOrigin + url;
    }

    return baseOrigin + basePath + '/' + url;
  }

  // Format Swagger JSON into readable text for embedding
  private formatSwaggerJson(swaggerJson: any): string {
    try {
      const parts: string[] = [];

      // Add basic info
      if (swaggerJson.info) {
        parts.push(`API: ${swaggerJson.info.title || 'Unknown'}`);
        if (swaggerJson.info.description) {
          parts.push(`Description: ${swaggerJson.info.description}`);
        }
        if (swaggerJson.info.version) {
          parts.push(`Version: ${swaggerJson.info.version}`);
        }
      }

      // Add server information
      if (swaggerJson.servers && swaggerJson.servers.length > 0) {
        parts.push('\nServers:');
        swaggerJson.servers.forEach((server: any) => {
          parts.push(`- ${server.url}${server.description ? ': ' + server.description : ''}`);
        });
      } else if (swaggerJson.host) {
        parts.push(`\nHost: ${swaggerJson.host}`);
        if (swaggerJson.basePath) {
          parts.push(`Base Path: ${swaggerJson.basePath}`);
        }
      }

      // Add security schemes
      if (swaggerJson.securityDefinitions || swaggerJson.components?.securitySchemes) {
        const securitySchemes = swaggerJson.securityDefinitions || swaggerJson.components?.securitySchemes;
        parts.push('\nSecurity:');
        Object.entries(securitySchemes).forEach(([name, scheme]: [string, any]) => {
          parts.push(`- ${name}: ${scheme.type} ${scheme.description ? '- ' + scheme.description : ''}`);
        });
      }

      // Add paths/endpoints with detailed information
      if (swaggerJson.paths) {
        parts.push('\nAPI Endpoints:');

        Object.entries(swaggerJson.paths).forEach(([path, pathObj]: [string, any]) => {
          Object.entries(pathObj).forEach(([method, operation]: [string, any]) => {
            if (typeof operation === 'object' && operation !== null) {
              const summary = operation.summary || '';
              const description = operation.description || '';
              parts.push(`\n${method.toUpperCase()} ${path}`);
              if (summary) parts.push(`  Summary: ${summary}`);
              if (description) parts.push(`  Description: ${description}`);

              // Add tags
              if (operation.tags && operation.tags.length > 0) {
                parts.push(`  Tags: ${operation.tags.join(', ')}`);
              }

              // Add security requirements
              if (operation.security && operation.security.length > 0) {
                parts.push('  Security:');
                operation.security.forEach((sec: any) => {
                  Object.keys(sec).forEach(secName => {
                    parts.push(`    - ${secName}: ${sec[secName].join(', ') || 'required'}`);
                  });
                });
              }

              // Add consumes/produces
              if (operation.consumes && operation.consumes.length > 0) {
                parts.push(`  Consumes: ${operation.consumes.join(', ')}`);
              }
              if (operation.produces && operation.produces.length > 0) {
                parts.push(`  Produces: ${operation.produces.join(', ')}`);
              }

              // Add parameters with detailed information
              if (operation.parameters && operation.parameters.length > 0) {
                parts.push('  Parameters:');
                operation.parameters.forEach((param: any) => {
                  const paramInfo = [`${param.name} (${param.in})`];
                  if (param.type) paramInfo.push(`type: ${param.type}`);
                  if (param.required) paramInfo.push('required');
                  if (param.description) paramInfo.push(`description: ${param.description}`);
                  if (param.example !== undefined) paramInfo.push(`example: ${JSON.stringify(param.example)}`);
                  if (param.default !== undefined) paramInfo.push(`default: ${JSON.stringify(param.default)}`);
                  if (param.enum) paramInfo.push(`enum: [${param.enum.join(', ')}]`);
                  parts.push(`    - ${paramInfo.join(', ')}`);
                });
              }

              // Add request body schema (OpenAPI 3.0)
              if (operation.requestBody) {
                parts.push('  Request Body:');
                if (operation.requestBody.description) {
                  parts.push(`    Description: ${operation.requestBody.description}`);
                }
                if (operation.requestBody.content) {
                  Object.entries(operation.requestBody.content).forEach(([mediaType, content]: [string, any]) => {
                    parts.push(`    ${mediaType}:`);
                    if (content.schema) {
                      const schemaInfo = this.formatSchemaInfo(content.schema, swaggerJson);
                      parts.push(`      Schema: ${schemaInfo}`);
                    }
                    if (content.example) {
                      parts.push(`      Example: ${JSON.stringify(content.example, null, 2)}`);
                    }
                  });
                }
              }

              // Add responses with comprehensive schema information and examples
              if (operation.responses) {
                parts.push('  Responses:');
                Object.entries(operation.responses).forEach(([code, response]: [string, any]) => {
                  parts.push(`    ${code}: ${response.description || 'No description'}`);

                  // Add response schema information (Swagger 2.0)
                  if (response.schema) {
                    const detailedSchema = this.formatDetailedSchemaWithExamples(response.schema, swaggerJson, 0);
                    parts.push(`      Schema: ${detailedSchema}`);
                  }

                  // Add response content (OpenAPI 3.0)
                  if (response.content) {
                    Object.entries(response.content).forEach(([mediaType, content]: [string, any]) => {
                      parts.push(`      Content-Type: ${mediaType}`);
                      if (content.schema) {
                        const detailedSchema = this.formatDetailedSchemaWithExamples(content.schema, swaggerJson, 0);
                        parts.push(`        Schema: ${detailedSchema}`);
                      }
                      if (content.example) {
                        parts.push(`        Response Example:`);
                        parts.push(`${JSON.stringify(content.example, null, 2)}`);
                      }
                      if (content.examples) {
                        parts.push(`        Response Examples:`);
                        Object.entries(content.examples).forEach(([exampleName, example]: [string, any]) => {
                          parts.push(`          ${exampleName}:`);
                          if (example.value) {
                            parts.push(`${JSON.stringify(example.value, null, 2)}`);
                          }
                        });
                      }
                    });
                  }

                  // Add examples (Swagger 2.0)
                  if (response.examples) {
                    parts.push('      Response Examples:');
                    Object.entries(response.examples).forEach(([exampleType, example]: [string, any]) => {
                      parts.push(`        ${exampleType}:`);
                      parts.push(`${JSON.stringify(example, null, 2)}`);
                    });
                  }
                });
              }
            }
          });
        });
      }

      // Add detailed schema definitions with full expansion
      if (swaggerJson.components?.schemas || swaggerJson.definitions) {
        const schemas = swaggerJson.components?.schemas || swaggerJson.definitions;
        parts.push('\nDetailed Data Models and Schemas:');

        // Sort schemas to prioritize commonly used ones
        const sortedSchemas = Object.entries(schemas).sort(([a], [b]) => {
          // Prioritize Response, Error, and common schemas
          const priority = (name: string) => {
            if (name.toLowerCase().includes('response')) return 1;
            if (name.toLowerCase().includes('error')) return 2;
            if (name.toLowerCase().includes('result')) return 3;
            if (name.toLowerCase().includes('data')) return 4;
            return 5;
          };
          return priority(a) - priority(b);
        });

        sortedSchemas.forEach(([name, schema]: [string, any]) => {
          parts.push(`\n=== ${name} Schema ===`);

          if (schema.description) {
            parts.push(`Description: ${schema.description}`);
          }

          // Add type information
          if (schema.type) {
            parts.push(`Type: ${schema.type}`);
          }

          // Handle polymorphism and inheritance (OpenAPI 3.0 features)
          if (schema.allOf || schema.oneOf || schema.anyOf) {
            parts.push(this.formatPolymorphicSchema(schema, swaggerJson, name));
          }

          // Add discriminator information
          if (schema.discriminator) {
            parts.push(`Discriminator: ${schema.discriminator.propertyName || schema.discriminator}`);
            if (schema.discriminator.mapping) {
              parts.push('Discriminator Mapping:');
              Object.entries(schema.discriminator.mapping).forEach(([key, value]: [string, any]) => {
                parts.push(`  ${key} -> ${value}`);
              });
            }
          }

          // Add properties with full detailed information
          if (schema.properties) {
            parts.push('Properties:');
            Object.entries(schema.properties).forEach(([propName, prop]: [string, any]) => {
              const required = schema.required && schema.required.includes(propName) ? ' (REQUIRED)' : ' (optional)';
              parts.push(`  • ${propName}${required}:`);

              // Get detailed property information
              const propDetails = this.getDetailedPropertyInfo(prop, swaggerJson);
              propDetails.forEach(detail => parts.push(`    ${detail}`));
            });
          }

          // Handle additional properties
          if (schema.additionalProperties !== undefined) {
            if (schema.additionalProperties === true) {
              parts.push('Additional Properties: Allowed (any type)');
            } else if (schema.additionalProperties === false) {
              parts.push('Additional Properties: Not allowed');
            } else if (typeof schema.additionalProperties === 'object') {
              const additionalInfo = this.formatSchemaInfo(schema.additionalProperties, swaggerJson);
              parts.push(`Additional Properties: ${additionalInfo}`);
            }
          }

          // Add example if available with proper formatting
          if (schema.example) {
            parts.push('Example:');
            parts.push(`${JSON.stringify(schema.example, null, 2)}`);
          }

          // Add enum values
          if (schema.enum) {
            parts.push(`Possible values: ${schema.enum.join(', ')}`);
          }

          // Handle allOf, oneOf, anyOf in definitions with full expansion
          if (schema.allOf) {
            parts.push('Composed of (allOf):');
            schema.allOf.forEach((subSchema: any, index: number) => {
              if (subSchema.$ref) {
                const refSchema = this.resolveSchemaRef(subSchema.$ref, swaggerJson);
                const refName = subSchema.$ref.split('/').pop();
                parts.push(`  ${index + 1}. Inherits from ${refName}:`);
                if (refSchema && refSchema.properties) {
                  Object.entries(refSchema.properties).forEach(([propName, prop]: [string, any]) => {
                    const propDetails = this.getDetailedPropertyInfo(prop, swaggerJson);
                    parts.push(`    • ${propName}: ${propDetails.join(', ')}`);
                  });
                }
              } else {
                const subInfo = this.formatSchemaInfo(subSchema, swaggerJson, 1);
                parts.push(`  ${index + 1}. ${subInfo}`);
              }
            });
          }

          if (schema.oneOf) {
            parts.push('One of (oneOf):');
            schema.oneOf.forEach((subSchema: any, index: number) => {
              const subInfo = this.formatSchemaInfo(subSchema, swaggerJson, 1);
              parts.push(`  ${index + 1}. ${subInfo}`);
            });
          }

          // Add validation constraints
          const constraints: string[] = [];
          if (schema.minimum !== undefined) constraints.push(`minimum: ${schema.minimum}`);
          if (schema.maximum !== undefined) constraints.push(`maximum: ${schema.maximum}`);
          if (schema.minLength !== undefined) constraints.push(`minLength: ${schema.minLength}`);
          if (schema.maxLength !== undefined) constraints.push(`maxLength: ${schema.maxLength}`);
          if (schema.pattern) constraints.push(`pattern: ${schema.pattern}`);
          if (constraints.length > 0) {
            parts.push(`Constraints: ${constraints.join(', ')}`);
          }
        });
      }

      return parts.join('\n');
    } catch (error) {
      this.logger.warn('Error formatting Swagger JSON:', error);
      return JSON.stringify(swaggerJson, null, 2);
    }
  }

  // Format polymorphic schemas (allOf, oneOf, anyOf)
  private formatPolymorphicSchema(schema: any, swaggerJson: any, schemaName: string): string {
    const parts: string[] = [];

    // Handle allOf (inheritance/composition)
    if (schema.allOf) {
      parts.push('Inheritance/Composition (allOf):');
      schema.allOf.forEach((subSchema: any, index: number) => {
        if (subSchema.$ref) {
          const refSchema = this.resolveSchemaRef(subSchema.$ref, swaggerJson);
          const refName = subSchema.$ref.split('/').pop();
          parts.push(`  ${index + 1}. Inherits from ${refName}`);

          if (refSchema) {
            // Show inherited properties
            if (refSchema.properties) {
              parts.push(`     Inherited properties:`);
              Object.entries(refSchema.properties).forEach(([propName, prop]: [string, any]) => {
                const propType = this.getPropertyType(prop, swaggerJson);
                const required = refSchema.required && refSchema.required.includes(propName) ? ' (required)' : '';
                parts.push(`       - ${propName}: ${propType}${required}`);
              });
            }
          }
        } else {
          // Inline schema
          parts.push(`  ${index + 1}. Inline schema:`);
          if (subSchema.properties) {
            Object.entries(subSchema.properties).forEach(([propName, prop]: [string, any]) => {
              const propType = this.getPropertyType(prop, swaggerJson);
              const required = subSchema.required && subSchema.required.includes(propName) ? ' (required)' : '';
              parts.push(`       - ${propName}: ${propType}${required}`);
            });
          }
        }
      });
    }

    // Handle oneOf (polymorphism - exactly one)
    if (schema.oneOf) {
      parts.push('Polymorphism - One Of (oneOf):');
      schema.oneOf.forEach((subSchema: any, index: number) => {
        if (subSchema.$ref) {
          const refName = subSchema.$ref.split('/').pop();
          parts.push(`  ${index + 1}. ${refName}`);
        } else {
          const schemaInfo = this.formatSchemaInfo(subSchema, swaggerJson, 1);
          parts.push(`  ${index + 1}. ${schemaInfo}`);
        }
      });
    }

    // Handle anyOf (polymorphism - any combination)
    if (schema.anyOf) {
      parts.push('Polymorphism - Any Of (anyOf):');
      schema.anyOf.forEach((subSchema: any, index: number) => {
        if (subSchema.$ref) {
          const refName = subSchema.$ref.split('/').pop();
          parts.push(`  ${index + 1}. ${refName}`);
        } else {
          const schemaInfo = this.formatSchemaInfo(subSchema, swaggerJson, 1);
          parts.push(`  ${index + 1}. ${schemaInfo}`);
        }
      });
    }

    return parts.join('\n');
  }

  // Helper method to get property type information
  private getPropertyType(prop: any, swaggerJson: any): string {
    if (prop.$ref) {
      const refName = prop.$ref.split('/').pop();
      return refName || 'reference';
    }

    if (prop.type === 'array' && prop.items) {
      if (prop.items.$ref) {
        const itemRefName = prop.items.$ref.split('/').pop();
        return `array of ${itemRefName}`;
      } else {
        return `array of ${prop.items.type || 'unknown'}`;
      }
    }

    if (prop.type) {
      let typeInfo = prop.type;
      if (prop.format) {
        typeInfo += ` (${prop.format})`;
      }
      if (prop.enum) {
        typeInfo += ` [${prop.enum.join('|')}]`;
      }
      return typeInfo;
    }

    return 'unknown';
  }

  // Helper method to resolve schema references and get full schema definition
  private resolveSchemaRef(ref: string, swaggerJson: any): any {
    const refPath = ref.replace('#/', '').split('/');
    let refSchema = swaggerJson;
    for (const part of refPath) {
      refSchema = refSchema[part];
      if (!refSchema) break;
    }
    return refSchema;
  }

  // Helper method to get detailed property information
  private getDetailedPropertyInfo(prop: any, swaggerJson: any): string[] {
    const details: string[] = [];

    // Handle $ref references
    if (prop.$ref) {
      const refSchema = this.resolveSchemaRef(prop.$ref, swaggerJson);
      const refName = prop.$ref.split('/').pop();
      if (refSchema) {
        details.push(`Type: ${refName} (${refSchema.type || 'object'})`);
        if (refSchema.description) {
          details.push(`Description: ${refSchema.description}`);
        }
        if (refSchema.example) {
          details.push(`Example: ${JSON.stringify(refSchema.example)}`);
        }
      } else {
        details.push(`Type: ${refName} (reference)`);
      }
      return details;
    }

    // Add type information
    if (prop.type) {
      details.push(`Type: ${prop.type}`);
    }

    // Add format information
    if (prop.format) {
      details.push(`Format: ${prop.format}`);
    }

    // Add description
    if (prop.description) {
      details.push(`Description: ${prop.description}`);
    }

    // Add example
    if (prop.example !== undefined) {
      if (typeof prop.example === 'object') {
        details.push(`Example: ${JSON.stringify(prop.example)}`);
      } else {
        details.push(`Example: ${prop.example}`);
      }
    }

    // Add enum values
    if (prop.enum) {
      details.push(`Enum: [${prop.enum.join(', ')}]`);
    }

    // Add validation constraints
    if (prop.minimum !== undefined) details.push(`Min: ${prop.minimum}`);
    if (prop.maximum !== undefined) details.push(`Max: ${prop.maximum}`);
    if (prop.minLength !== undefined) details.push(`MinLength: ${prop.minLength}`);
    if (prop.maxLength !== undefined) details.push(`MaxLength: ${prop.maxLength}`);
    if (prop.pattern) details.push(`Pattern: ${prop.pattern}`);

    // Handle array items with enhanced information
    if (prop.type === 'array' && prop.items) {
      if (prop.items.$ref) {
        const refName = prop.items.$ref.split('/').pop();
        details.push(`Items: Array of ${refName}`);
      } else if (prop.items.type) {
        let itemType = prop.items.type;
        if (prop.items.format) itemType += ` (${prop.items.format})`;
        details.push(`Items: Array of ${itemType}`);
      } else {
        details.push(`Items: Array of unknown`);
      }

      // Add array constraints
      if (prop.minItems !== undefined) details.push(`MinItems: ${prop.minItems}`);
      if (prop.maxItems !== undefined) details.push(`MaxItems: ${prop.maxItems}`);
      if (prop.uniqueItems) details.push(`UniqueItems: true`);
    }

    // Handle object properties (limited to avoid too much nesting)
    if (prop.type === 'object' && prop.properties) {
      const propCount = Object.keys(prop.properties).length;
      details.push(`Properties: Object with ${propCount} properties`);

      // Show required properties for objects
      if (prop.required && prop.required.length > 0) {
        details.push(`Required: [${prop.required.join(', ')}]`);
      }
    }

    // Handle polymorphic properties (allOf, oneOf, anyOf)
    if (prop.allOf) {
      details.push(`Composition (allOf): ${prop.allOf.length} schemas`);
    }
    if (prop.oneOf) {
      details.push(`One of (oneOf): ${prop.oneOf.length} options`);
    }
    if (prop.anyOf) {
      details.push(`Any of (anyOf): ${prop.anyOf.length} options`);
    }

    // Handle nullable (OpenAPI 3.0)
    if (prop.nullable) {
      details.push(`Nullable: true`);
    }

    // Handle readOnly/writeOnly
    if (prop.readOnly) {
      details.push(`ReadOnly: true`);
    }
    if (prop.writeOnly) {
      details.push(`WriteOnly: true`);
    }

    // Handle deprecated
    if (prop.deprecated) {
      details.push(`Deprecated: true`);
    }

    // Handle default values
    if (prop.default !== undefined) {
      details.push(`Default: ${JSON.stringify(prop.default)}`);
    }

    // Handle additional properties for objects
    if (prop.additionalProperties !== undefined) {
      if (prop.additionalProperties === true) {
        details.push(`AdditionalProperties: allowed`);
      } else if (prop.additionalProperties === false) {
        details.push(`AdditionalProperties: not allowed`);
      } else {
        details.push(`AdditionalProperties: ${prop.additionalProperties.type || 'object'}`);
      }
    }

    return details.length > 0 ? details : ['Type: unknown'];
  }

  // Helper method to format detailed schema with examples and full object expansion
  private formatDetailedSchemaWithExamples(schema: any, swaggerJson: any, depth: number = 0): string {
    if (depth > 3) return '[nested object]';

    try {
      // Handle $ref references - resolve them fully with examples
      if (schema.$ref) {
        const refSchema = this.resolveSchemaRef(schema.$ref, swaggerJson);
        if (refSchema) {
          const refName = schema.$ref.split('/').pop();

          // For top-level schemas, show full details
          if (depth === 0) {
            return this.formatCompleteSchemaStructure(refSchema, swaggerJson, refName);
          } else {
            return `${refName} (${refSchema.type || 'object'})`;
          }
        }
        return schema.$ref;
      }

      // For object schemas, show complete structure
      if (schema.type === 'object' || (!schema.type && schema.properties)) {
        return this.formatCompleteSchemaStructure(schema, swaggerJson);
      }

      // For other types, use the existing detailed formatting
      return this.formatSchemaInfo(schema, swaggerJson, depth);
    } catch (error) {
      return 'schema parsing error';
    }
  }

  // Helper method to format complete schema structure with all properties
  private formatCompleteSchemaStructure(schema: any, swaggerJson: any, schemaName?: string): string {
    const parts: string[] = [];

    if (schemaName) {
      parts.push(`${schemaName} {`);
    } else {
      parts.push(`Object {`);
    }

    // Add description
    if (schema.description) {
      parts.push(`  // ${schema.description}`);
    }

    // Add all properties with detailed information
    if (schema.properties) {
      Object.entries(schema.properties).forEach(([propName, propSchema]: [string, any]) => {
        const required = schema.required && schema.required.includes(propName) ? ' (required)' : '';
        const propDetails = this.formatPropertyWithType(propSchema, swaggerJson);
        parts.push(`  ${propName}${required}: ${propDetails}`);
      });
    }

    // Handle allOf compositions
    if (schema.allOf) {
      schema.allOf.forEach((subSchema: any) => {
        if (subSchema.$ref) {
          const refSchema = this.resolveSchemaRef(subSchema.$ref, swaggerJson);
          const refName = subSchema.$ref.split('/').pop();
          parts.push(`  // Inherits from ${refName}`);
          if (refSchema && refSchema.properties) {
            Object.entries(refSchema.properties).forEach(([propName, propSchema]: [string, any]) => {
              const propDetails = this.formatPropertyWithType(propSchema, swaggerJson);
              parts.push(`  ${propName}: ${propDetails}`);
            });
          }
        } else if (subSchema.properties) {
          Object.entries(subSchema.properties).forEach(([propName, propSchema]: [string, any]) => {
            const propDetails = this.formatPropertyWithType(propSchema, swaggerJson);
            parts.push(`  ${propName}: ${propDetails}`);
          });
        }
      });
    }

    parts.push(`}`);

    // Add example if available
    if (schema.example) {
      parts.push(`Example: ${JSON.stringify(schema.example, null, 2)}`);
    }

    return parts.join('\n');
  }

  // Helper method to format property with type information
  private formatPropertyWithType(propSchema: any, swaggerJson: any): string {
    if (propSchema.$ref) {
      const refSchema = this.resolveSchemaRef(propSchema.$ref, swaggerJson);
      const refName = propSchema.$ref.split('/').pop();
      if (refSchema) {
        if (refSchema.type === 'object' && refSchema.properties) {
          // For object references, show the structure
          const propCount = Object.keys(refSchema.properties).length;
          return `${refName} (object with ${propCount} properties)`;
        } else {
          return `${refName} (${refSchema.type || 'object'})`;
        }
      }
      return refName;
    }

    const parts: string[] = [];

    // Add type
    if (propSchema.type) {
      parts.push(propSchema.type);
    }

    // Add format
    if (propSchema.format) {
      parts.push(`(${propSchema.format})`);
    }

    // Add description
    if (propSchema.description) {
      parts.push(`// ${propSchema.description}`);
    }

    // Add example
    if (propSchema.example !== undefined) {
      parts.push(`example: ${JSON.stringify(propSchema.example)}`);
    }

    // Add enum values
    if (propSchema.enum) {
      parts.push(`enum: [${propSchema.enum.join(', ')}]`);
    }

    // Handle arrays
    if (propSchema.type === 'array' && propSchema.items) {
      const itemType = this.formatPropertyWithType(propSchema.items, swaggerJson);
      return `array of ${itemType}`;
    }

    // Handle nested objects (limited depth)
    if (propSchema.type === 'object' && propSchema.properties) {
      const propCount = Object.keys(propSchema.properties).length;
      parts.push(`object with ${propCount} properties`);
    }

    return parts.join(' ');
  }

  // Helper method to format schema information with full reference resolution and examples
  private formatSchemaInfo(schema: any, swaggerJson: any, depth: number = 0): string {
    if (depth > 4) return '[nested object]'; // Prevent infinite recursion

    try {
      // Handle $ref references - resolve them fully
      if (schema.$ref) {
        const refSchema = this.resolveSchemaRef(schema.$ref, swaggerJson);
        if (refSchema) {
          const refName = schema.$ref.split('/').pop();

          // For shallow depth, show detailed schema info
          if (depth < 2) {
            const refInfo = this.formatSchemaInfo(refSchema, swaggerJson, depth + 1);
            return `${refName}: ${refInfo}`;
          } else {
            // For deeper levels, just show the reference name and basic type
            return `${refName} (${refSchema.type || 'object'})`;
          }
        }
        return schema.$ref;
      }

      const parts: string[] = [];

      // Add type information
      if (schema.type) {
        parts.push(`type: ${schema.type}`);
      }

      // Add format information
      if (schema.format) {
        parts.push(`format: ${schema.format}`);
      }

      // Add description
      if (schema.description) {
        parts.push(`description: "${schema.description}"`);
      }

      // Add example with proper formatting
      if (schema.example !== undefined) {
        if (typeof schema.example === 'object') {
          parts.push(`example: ${JSON.stringify(schema.example, null, 2)}`);
        } else {
          parts.push(`example: ${schema.example}`);
        }
      }

      // Add enum values
      if (schema.enum) {
        parts.push(`enum: [${schema.enum.join(', ')}]`);
      }

      // Add array item information
      if (schema.type === 'array' && schema.items) {
        const itemInfo = this.formatSchemaInfo(schema.items, swaggerJson, depth + 1);
        parts.push(`items: [${itemInfo}]`);
      }

      // Add object properties with full details
      if (schema.type === 'object' && schema.properties && depth < 3) {
        const propParts: string[] = [];
        Object.entries(schema.properties).forEach(([propName, propSchema]: [string, any]) => {
          const propInfo = this.formatSchemaInfo(propSchema, swaggerJson, depth + 1);
          const required = schema.required && schema.required.includes(propName) ? ' (required)' : '';
          propParts.push(`${propName}: ${propInfo}${required}`);
        });
        if (propParts.length > 0) {
          parts.push(`properties: {${propParts.join(', ')}}`);
        }
      }

      // Handle allOf with full resolution
      if (schema.allOf) {
        const allOfParts: string[] = [];
        schema.allOf.forEach((subSchema: any, index: number) => {
          const subInfo = this.formatSchemaInfo(subSchema, swaggerJson, depth + 1);
          allOfParts.push(`${index + 1}. ${subInfo}`);
        });
        parts.push(`allOf: [${allOfParts.join(', ')}]`);
      }

      // Handle oneOf with full resolution
      if (schema.oneOf) {
        const oneOfParts: string[] = [];
        schema.oneOf.forEach((subSchema: any, index: number) => {
          const subInfo = this.formatSchemaInfo(subSchema, swaggerJson, depth + 1);
          oneOfParts.push(`${index + 1}. ${subInfo}`);
        });
        parts.push(`oneOf: [${oneOfParts.join(', ')}]`);
      }

      // Handle anyOf with full resolution
      if (schema.anyOf) {
        const anyOfParts: string[] = [];
        schema.anyOf.forEach((subSchema: any, index: number) => {
          const subInfo = this.formatSchemaInfo(subSchema, swaggerJson, depth + 1);
          anyOfParts.push(`${index + 1}. ${subInfo}`);
        });
        parts.push(`anyOf: [${anyOfParts.join(', ')}]`);
      }

      // Add validation constraints
      if (schema.minimum !== undefined) parts.push(`min: ${schema.minimum}`);
      if (schema.maximum !== undefined) parts.push(`max: ${schema.maximum}`);
      if (schema.minLength !== undefined) parts.push(`minLength: ${schema.minLength}`);
      if (schema.maxLength !== undefined) parts.push(`maxLength: ${schema.maxLength}`);
      if (schema.pattern) parts.push(`pattern: ${schema.pattern}`);
      if (schema.minItems !== undefined) parts.push(`minItems: ${schema.minItems}`);
      if (schema.maxItems !== undefined) parts.push(`maxItems: ${schema.maxItems}`);

      return parts.length > 0 ? parts.join(', ') : 'unknown';
    } catch (error) {
      return 'schema parsing error';
    }
  }

  async process(job: Job<EmbeddingJob>): Promise<void> {
    this.logger.debug(`Processing embedding job ${job.id}`);

    try {
      let text: string;

      // Extract text based on job type (file or URL)
      if (job.data.fileBuffer && job.data.mimetype) {
        // Process file
        text = await this.fileProcessorService.extractText({
          buffer: Buffer.from(job.data.fileBuffer),
          mimetype: job.data.mimetype,
        } as Express.Multer.File);
      } else if (job.data.url) {
        // Fetch and process URL content
        text = await this.processUrlContent(
          job.data.url,
          job.data.integration,
          job.data.integration_email && job.data.integration_api_token
            ? {
                email: job.data.integration_email,
                token: job.data.integration_api_token,
              }
            : undefined
        );
      } else {
        throw new Error('Invalid job data: requires either file or URL');
      }

      // Split text into chunks
      const chunks = this.fileProcessorService.splitIntoChunks(text);

      // Process chunks in batches to avoid rate limits
      const batchSize = 20;
      for (let i = 0; i < chunks.length; i += batchSize) {
        const batchChunks = chunks.slice(i, i + batchSize);
        
        // Create the embedding entities with correct ID field
        const embeddingEntities = batchChunks.map(content => ({
          content,
          embedding: [], // Will be updated after OpenAI call
          ...(job.data.isPublishUrl 
            ? { publishUrlId: job.data.fileId }
            : { fileUploadId: job.data.fileId }
          ),
        }));

        // Save the embedding entities first to get their IDs
        const savedEmbeddings = await this.embeddingRepository.save(embeddingEntities);
        
        // Create embeddings for the batch using all embedding IDs for token tracking
        const embeddings = await this.openAIService.createEmbeddings(
          batchChunks,
          savedEmbeddings.map(e => e.id)
        );

        // Update the embeddings with their vector data
        await Promise.all(
          savedEmbeddings.map((entity, index) =>
            this.embeddingRepository.update(entity.id, {
              embedding: embeddings[index]
            })
          )
        );

        // Update job progress
        await job.updateProgress((i + batchSize) / chunks.length * 100);

        // Add a small delay between batches to respect rate limits
        if (i + batchSize < chunks.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      this.logger.debug(`Completed embedding job ${job.id}`);
    } catch (error) {
      this.logger.error(`Error processing embedding job ${job.id}:`, error);
      throw error;
    }
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    this.logger.debug(`Job ${job.id} completed successfully`);
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job, error: Error) {
    this.logger.error(`Job ${job.id} failed:`, error);
  }
}
